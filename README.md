# Omnispace

**Omnispace** is a modern web-based interface for managing and connecting to Firecracker microVM desktops via VNC. Built with Next.js, TypeScript, and shadcn/ui, it provides a seamless cloud desktop experience with minimal overhead.

## 🚀 Features

- **MicroVM Management**: Create, start, stop, and delete Firecracker microVMs
- **VNC Desktop Access**: Connect to VM desktops directly in the browser using noVNC
- **Modern UI**: Clean, responsive interface built with shadcn/ui components
- **Real-time Status**: Live VM status monitoring and connection management
- **Resource Monitoring**: Track CPU, memory, and disk usage for each VM
- **Scalable Architecture**: Designed for both single-user and multi-tenant deployments

## 🏗️ Architecture

### Frontend
- **Next.js 15** with App Router
- **TypeScript** for type safety
- **Tailwind CSS** for styling
- **shadcn/ui** for component library
- **noVNC** for browser-based VNC client
- **Lucide React** for icons

### Backend Integration
- **WebSocket** connections for VNC
- **REST API** for VM management (to be implemented)
- **Firecracker VMM** for lightweight virtualization

## 📋 Prerequisites

- Node.js 18+ or 20+
- pnpm (recommended) or npm
- Firecracker VMM (for backend integration)
- Linux host system (for Firecracker)

## 🛠️ Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd omnispace
   ```

2. **Install dependencies**:
   ```bash
   pnpm install
   ```

3. **Run the development server**:
   ```bash
   pnpm dev
   ```

4. **Open your browser** and navigate to `http://localhost:3000`

## 🔧 Configuration

### Environment Variables

Create a `.env.local` file in the project root:

```env
# VNC Configuration
VNC_BASE_PORT=5900
VNC_HOST=localhost

# Firecracker Configuration
FIRECRACKER_SOCKET_PATH=/tmp/firecracker.socket
FIRECRACKER_KERNEL_PATH=/opt/firecracker/vmlinux.bin
FIRECRACKER_ROOTFS_PATH=/opt/firecracker/rootfs.img

# API Configuration
API_BASE_URL=http://localhost:3001
WEBSOCKET_URL=ws://localhost:3001
```

## 🖥️ VM Templates

Omnispace supports various VM templates:

### Ubuntu Desktop
- **OS**: Ubuntu 22.04 LTS with GNOME
- **Default Resources**: 2 CPU, 2GB RAM, 20GB disk
- **VNC Server**: TigerVNC

### Development Environment
- **OS**: Ubuntu 22.04 with development tools
- **Default Resources**: 4 CPU, 4GB RAM, 40GB disk
- **Includes**: VS Code, Docker, Git, Node.js

### Minimal Desktop
- **OS**: Alpine Linux with XFCE
- **Default Resources**: 1 CPU, 1GB RAM, 10GB disk
- **VNC Server**: x11vnc

## 🔌 API Integration

### VM Management Endpoints

```typescript
// Get all VMs
GET /api/vms
Response: MicroVM[]

// Create new VM
POST /api/vms
Body: VMConfiguration
Response: MicroVM

// Start VM
POST /api/vms/:id/start
Response: { status: 'starting' }

// Stop VM
POST /api/vms/:id/stop
Response: { status: 'stopping' }

// Delete VM
DELETE /api/vms/:id
Response: { deleted: true }

// Get VNC connection info
GET /api/vms/:id/vnc
Response: VNCConnection
```

## 🚀 Deployment

### Development
```bash
pnpm dev
```

### Production Build
```bash
pnpm build
pnpm start
```

### Docker Deployment
```bash
# Build image
docker build -t omnispace .

# Run container
docker run -p 3000:3000 omnispace
```

## 🔐 Security Considerations

- **VNC Authentication**: Configure VNC passwords for each VM
- **Network Isolation**: Use proper network namespaces for VM isolation
- **Access Control**: Implement user authentication and authorization
- **Resource Limits**: Set appropriate CPU, memory, and disk quotas
- **Firewall Rules**: Configure iptables for VM network access

## 🛠️ Backend Setup (Firecracker)

To fully utilize Omnispace, you'll need a backend service that manages Firecracker VMs:

### Prerequisites
```bash
# Install Firecracker
curl -LOJ https://github.com/firecracker-microvm/firecracker/releases/latest/download/firecracker-v1.4.1-x86_64.tgz
tar -xzf firecracker-v1.4.1-x86_64.tgz
sudo cp release-v1.4.1-x86_64/firecracker-v1.4.1-x86_64 /usr/local/bin/firecracker

# Install kernel and rootfs images
mkdir -p /opt/firecracker
# Download kernel and rootfs images
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit your changes: `git commit -m 'Add amazing feature'`
4. Push to the branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License.

## 🙏 Acknowledgments

- [Firecracker](https://firecracker-microvm.github.io/) - Lightweight virtualization technology
- [noVNC](https://novnc.com/) - HTML5 VNC client
- [shadcn/ui](https://ui.shadcn.com/) - Beautiful UI components
- [Next.js](https://nextjs.org/) - React framework for production

---

**Built with ❤️ for the cloud-native desktop experience**
