'use client';

import React, { useState } from 'react';
import { VMManager } from '@/components/VMManager';
import { VNCClient } from '@/components/VNCClient';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Cloud } from 'lucide-react';
import { Toaster } from '@/components/ui/sonner';

interface MicroVM {
  id: string;
  name: string;
  status: 'running' | 'stopped' | 'starting' | 'stopping';
  cpu: number;
  memory: number;
  diskSize: number;
  vncPort: number;
  createdAt: Date;
  lastAccessed?: Date;
}

export default function Home() {
  const [selectedVM, setSelectedVM] = useState<MicroVM | null>(null);

  const handleVMSelect = (vm: MicroVM) => {
    setSelectedVM(vm);
  };

  const handleDisconnect = () => {
    setSelectedVM(null);
  };

  const getVNCUrl = (vm: MicroVM) => {
    // In a real implementation, this would be the actual VNC WebSocket URL
    return `ws://localhost:${vm.vncPort}`;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <header className="mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              {selectedVM && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleDisconnect}
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to VMs
                </Button>
              )}
              <div className="flex items-center gap-3">
                <div className="flex items-center justify-center w-12 h-12 bg-blue-600 rounded-lg">
                  <Cloud className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                    Omnispace
                  </h1>
                  <p className="text-gray-600 dark:text-gray-400">
                    Firecracker MicroVM Desktop Access
                  </p>
                </div>
              </div>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main>
          {selectedVM ? (
            <div className="space-y-4">
              <div className="flex items-center gap-4 mb-6">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                  Connected to {selectedVM.name}
                </h2>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  VNC Port: {selectedVM.vncPort} | {selectedVM.cpu} CPU | {selectedVM.memory}MB RAM
                </div>
              </div>
              <VNCClient
                url={getVNCUrl(selectedVM)}
                vmName={selectedVM.name}
                onDisconnect={handleDisconnect}
              />
            </div>
          ) : (
            <VMManager onSelectVM={handleVMSelect} />
          )}
        </main>
      </div>
      <Toaster />
    </div>
  );
}
