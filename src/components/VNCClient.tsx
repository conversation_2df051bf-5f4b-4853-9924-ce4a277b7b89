'use client';

import React, { useEffect, useRef, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Monitor, Power, Maximize2, Minimize2 } from 'lucide-react';

// Dynamic import for noVNC to avoid SSR issues
let RFB: any = null;

interface VNCClientProps {
  url: string;
  vmName: string;
  onDisconnect?: () => void;
}

export const VNCClient: React.FC<VNCClientProps> = ({ url, vmName, onDisconnect }) => {
  const canvasRef = useRef<HTMLDivElement>(null);
  const rfbRef = useRef<RFB | null>(null);
  const [connected, setConnected] = useState(false);
  const [connecting, setConnecting] = useState(false);
  const [fullscreen, setFullscreen] = useState(false);

  useEffect(() => {
    return () => {
      if (rfbRef.current) {
        rfbRef.current.disconnect();
      }
    };
  }, []);

  const connect = async () => {
    if (!canvasRef.current || connecting || connected) return;

    setConnecting(true);
    
    try {
      // Dynamic import of noVNC to avoid SSR issues
      if (!RFB) {
        const novnc = await import('@novnc/novnc/core/rfb');
        RFB = novnc.default;
      }
      
      const rfb = new RFB(canvasRef.current, url);
      
      rfb.addEventListener('connect', () => {
        console.log('Connected to VNC server');
        setConnected(true);
        setConnecting(false);
      });

      rfb.addEventListener('disconnect', (e: any) => {
        console.log('Disconnected from VNC server:', e.detail);
        setConnected(false);
        setConnecting(false);
        rfbRef.current = null;
        if (onDisconnect) onDisconnect();
      });

      rfb.addEventListener('credentialsrequired', () => {
        console.log('Credentials required');
        // Handle authentication if needed
      });

      rfb.scaleViewport = true;
      rfb.resizeSession = true;
      
      rfbRef.current = rfb;
    } catch (error) {
      console.error('Failed to connect:', error);
      setConnecting(false);
    }
  };

  const disconnect = () => {
    if (rfbRef.current) {
      rfbRef.current.disconnect();
    }
  };

  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      canvasRef.current?.requestFullscreen();
      setFullscreen(true);
    } else {
      document.exitFullscreen();
      setFullscreen(false);
    }
  };

  return (
    <Card className="w-full h-full">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Monitor className="h-5 w-5" />
            {vmName}
          </CardTitle>
          <div className="flex gap-2">
            {connected && (
              <Button
                variant="outline"
                size="sm"
                onClick={toggleFullscreen}
              >
                {fullscreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
              </Button>
            )}
            <Button
              variant={connected ? "destructive" : "default"}
              size="sm"
              onClick={connected ? disconnect : connect}
              disabled={connecting}
            >
              <Power className="h-4 w-4 mr-2" />
              {connecting ? 'Connecting...' : connected ? 'Disconnect' : 'Connect'}
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div
          ref={canvasRef}
          className="w-full h-[600px] bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700"
          style={{ minHeight: '400px' }}
        />
        {!connected && !connecting && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-50 dark:bg-gray-900 rounded-lg">
            <div className="text-center">
              <Monitor className="h-16 w-16 mx-auto mb-4 text-gray-400" />
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Click Connect to access {vmName}
              </p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
